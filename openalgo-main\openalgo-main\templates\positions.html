{% extends 'base.html' %}

{% block head %}
<style>
    .stat-card {
        @apply bg-base-100 rounded-lg shadow-lg p-6 transition-all duration-300;
    }
    
    .stat-card:hover {
        @apply shadow-xl transform -translate-y-1;
    }

    .table-container {
        @apply overflow-x-auto bg-base-100 rounded-lg shadow-lg mt-8;
    }

    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .position-long {
        @apply text-success font-semibold;
    }

    .position-short {
        @apply text-error font-semibold;
    }

    .position-neutral {
        @apply text-base-content font-semibold;
    }

    .quick-action-btn {
        @apply btn btn-sm;
    }

    /* Ensure dropdown is visible */
    .dropdown.dropdown-end .dropdown-content {
        @apply mt-2;
        min-width: 12rem;
        z-index: 50;
    }

    /* Add some padding to menu items */
    .dropdown-content .menu li > * {
        @apply py-3 px-4;
    }

    /* Ensure last row dropdown is visible */
    tr:last-child .dropdown .dropdown-content {
        bottom: 100%;
        top: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Header Section -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold">Trading Positions</h1>
            <p class="text-base-content/60">Monitor and manage your active trading positions</p>
        </div>
        <div class="flex gap-2">
            <button onclick="openCloseAllModal()" class="btn btn-error">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Close All
            </button>
            <a href="{{ url_for('orders_bp.export_positions') }}" class="btn btn-primary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
                Export to CSV
            </a>
        </div>
    </div>

    <!-- Positions Table -->
    <div id="positions-table-container" class="table-container">
        <table class="table w-full table-zebra">
            <thead class="table-header">
                <tr>
                    <th class="cursor-pointer hover:bg-base-300">
                        Trading Symbol
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                        </svg>
                    </th>
                    <th>Exchange</th>
                    <th>Product Type</th>
                    <th>Net Qty</th>
                    <th>Avg Price</th>
                    <th>LTP</th>
                    <th>P&L</th>
                    <th class="w-20">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for position in positions_data %}
                <tr class="hover:bg-base-200">
                    <td class="font-medium">{{ position.symbol }}</td>
                    <td>
                        {% set exchange_colors = {
                            'NSE': 'badge-accent',
                            'BSE': 'badge-neutral',
                            'NFO': 'badge-secondary',
                            'MCX': 'badge-primary'
                        } %}
                        <div class="badge {{ exchange_colors.get(position.exchange, 'badge-ghost') }}">
                            {{ position.exchange }}
                        </div>
                    </td>
                    <td>
                        {% set product_type_colors = {
                            'CNC': 'badge-secondary',
                            'MIS': 'badge-accent',
                            'NRML': 'badge-neutral'
                        } %}
                        <div class="badge {{ product_type_colors.get(position.product, 'badge-ghost') }}">
                            {{ position.product }}
                        </div>
                    </td>
                    <td class="{% if position.quantity|int > 0 %}position-long{% elif position.quantity|int < 0 %}position-short{% else %}position-neutral{% endif %}">
                        {{ position.quantity }}
                    </td>
                    <td class="font-mono">{{ position.average_price }}</td>
                    <td class="font-mono">{{ position.ltp if position.ltp is defined else 'N/A' }}</td>
                    <td class="{% if position.pnl|default(0)|float > 0 %}position-long{% elif position.pnl|default(0)|float < 0 %}position-short{% else %}position-neutral{% endif %}">
                        {{ position.pnl if position.pnl is defined else '0.00' }}
                    </td>
                    <td>
                        <div class="dropdown dropdown-end">
                            <label tabindex="0" class="btn btn-ghost btn-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                </svg>
                            </label>
                            <ul tabindex="0" class="dropdown-content menu menu-compact shadow-lg bg-base-100 rounded-box w-36">
                                <li>
                                    <a class="text-base-content" onclick="exitPosition('{{ position.symbol }}', '{{ position.exchange }}', '{{ position.product }}')">
                                        <span class="text-sm">Exit</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add sorting functionality to table headers
    document.querySelectorAll('th.cursor-pointer').forEach(headerCell => {
        headerCell.addEventListener('click', () => {
            const table = headerCell.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(headerCell.parentElement.children).indexOf(headerCell);
            
            // Get current sort direction
            const currentDirection = headerCell.getAttribute('data-sort') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update sort direction
            headerCell.setAttribute('data-sort', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                
                // Check if values are numbers
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return newDirection === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                return newDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Clear and repopulate tbody
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});

// Position management functions
function exitPosition(symbol, exchange, product) {
    // Call the close_position endpoint directly without confirmation
    const csrfToken = getCSRFToken(); // Get CSRF token from base.html helper
    fetch('/close_position', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken // Add CSRF token to headers
        },
        body: JSON.stringify({
            symbol: symbol,
            exchange: exchange,
            product: product
        })
    })
    .then(response => response.json())
    .then(data => {
        // Show only success message, not processing
        if (data.status === 'success') {
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'error');
        }
        
        // Refresh positions after a delay to avoid rate limiting
        setTimeout(() => {
            refreshCurrentPageContent();
        }, 1000);
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to exit position. Please try again.', 'error');
    });
}

function closeAllPositions() {
    // Close the modal
    document.getElementById('close-all-modal').checked = false;
    
    // Call the close_all_positions endpoint
    fetch('/close_all_positions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        // Show toast notification
        if (data.status === 'success') {
            showToast(data.message, 'success');
            // Reload the page after a short delay to show updated positions
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while closing all positions.', 'error');
    });
}

function openCloseAllModal() {
    document.getElementById('close-all-modal').checked = true;
}

function modifyPosition(symbol, exchange) {
    console.log(`Modifying position for ${symbol} on ${exchange}`);
}
</script>

<!-- DaisyUI Modal for Close All confirmation -->
<input type="checkbox" id="close-all-modal" class="modal-toggle" />
<div class="modal" role="dialog">
    <div class="modal-box">
        <h3 class="font-bold text-lg text-error">⚠️ Close All Positions</h3>
        <p class="py-4">Are you sure you want to close all open positions? This action will exit all your current trades immediately at market price.</p>
        <div class="modal-action">
            <label for="close-all-modal" class="btn">Cancel</label>
            <button onclick="closeAllPositions()" class="btn btn-error">Yes, Close All</button>
        </div>
    </div>
    <label class="modal-backdrop" for="close-all-modal"></label>
</div>
{% endblock %}
