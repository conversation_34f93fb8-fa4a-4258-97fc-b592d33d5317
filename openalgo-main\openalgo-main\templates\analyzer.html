{% extends "base.html" %}

{% block head %}
<style>
    .stat-card {
        @apply bg-base-100 rounded-lg shadow-lg p-6 transition-all duration-300;
    }
    
    .stat-card:hover {
        @apply shadow-xl transform -translate-y-1;
    }

    .table-container {
        @apply overflow-x-auto bg-base-100 rounded-lg shadow-lg;
    }

    .table-header {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .numeric-cell {
        @apply font-mono text-right;
    }

    .badge-cell {
        @apply text-center whitespace-nowrap min-w-[120px] px-2;
    }

    .badge-container {
        @apply flex justify-center items-center;
    }

    .badge {
        @apply truncate max-w-full;
    }

    @media (max-width: 640px) {
        .badge {
            @apply text-xs px-2 py-1 max-w-[120px];
        }
        .badge-cell {
            @apply min-w-[120px] px-1;
        }
        .table td {
            @apply px-2 py-2;
        }
        .status-badge {
            @apply whitespace-nowrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Header Section -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold">API Request Analyzer</h1>
        <p class="text-base-content/60">Monitor and analyze your API requests</p>
        
        <!-- Date Filter and Export Section -->
        <div class="mt-4 flex flex-wrap gap-4 items-center">
            <form id="dateFilterForm" class="flex flex-wrap gap-4 items-center" method="get">
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">Start Date</span>
                    </label>
                    <input type="date" name="start_date" class="input input-bordered" value="{{ start_date or '' }}">
                </div>
                <div class="form-control">
                    <label class="label">
                        <span class="label-text">End Date</span>
                    </label>
                    <input type="date" name="end_date" class="input input-bordered" value="{{ end_date or '' }}">
                </div>
                <div class="form-control mt-8">
                    <button type="submit" class="btn btn-primary">Filter</button>
                </div>
            </form>
            
            <div class="form-control mt-8">
                <a href="{{ url_for('analyzer_bp.export_requests', start_date=start_date, end_date=end_date) }}" 
                   class="btn btn-secondary">
                    Export to CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div id="stats-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Requests -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Total Requests</div>
                <div class="stat-value text-primary" id="total-requests">{{ stats.total_requests }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-primary">Last 24 hours</div>
                </div>
            </div>
        </div>

        <!-- Issues Found -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Issues Found</div>
                <div class="stat-value text-warning" id="total-issues">{{ stats.issues.total }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-warning">Needs Attention</div>
                </div>
            </div>
        </div>

        <!-- Unique Symbols -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Unique Symbols</div>
                <div class="stat-value text-accent" id="unique-symbols">{{ stats.symbols|length }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge badge-accent">Tracked</div>
                </div>
            </div>
        </div>

        <!-- Active Sources -->
        <div class="stat-card">
            <div class="stat">
                <div class="stat-title">Active Sources</div>
                <div class="stat-value" id="active-sources">{{ stats.sources|length }}</div>
                <div class="stat-desc mt-2">
                    <div class="badge">Connected</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Requests Table -->
    <div class="table-container">
        <table class="table w-full">
            <thead class="table-header">
                <tr>
                    <th class="cursor-pointer hover:bg-base-300">
                        Timestamp
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                        </svg>
                    </th>
                    <th>API Type</th>
                    <th>Source</th>
                    <th>Details</th>
                    <th>Exchange</th>
                    <th>Action</th>
                    <th>Status</th>
                    <th>View</th>
                </tr>
            </thead>
            <tbody id="requests-table">
                {% for request in requests %}
                <tr class="hover:bg-base-200">
                    <td class="text-sm">{{ request.timestamp }}</td>
                    <td class="badge-cell">
                        <div class="badge-container">
                            <div class="badge badge-primary">{{ request.api_type }}</div>
                        </div>
                    </td>
                    <td class="badge-cell">
                        <div class="badge-container">
                            <div class="badge badge-neutral truncate">{{ request.source }}</div>
                        </div>
                    </td>
                    <td class="font-medium">
                        {% if request.api_type == 'cancelorder' %}
                            OrderID: {{ request.orderid }}
                        {% else %}
                            {{ request.symbol }} {% if request.quantity %}({{ request.quantity }}){% endif %}
                            {% if request.api_type == 'placesmartorder' and request.position_size %}
                                [Size: {{ request.position_size }}]
                            {% endif %}
                        {% endif %}
                    </td>
                    <td class="badge-cell">
                        {% if request.exchange %}
                        {% set exchange_colors = {
                            'NSE': 'badge-accent',
                            'NFO': 'badge-secondary',
                            'CDS': 'badge-info',
                            'BSE': 'badge-neutral',
                            'BFO': 'badge-warning',
                            'BCD': 'badge-error',
                            'MCX': 'badge-primary',
                            'NCDEX': 'badge-success'
                        } %}
                        <div class="badge-container">
                            <div class="badge {{ exchange_colors.get(request.exchange, 'badge-ghost') }}">
                                {{ request.exchange }}
                            </div>
                        </div>
                        {% endif %}
                    </td>
                    <td class="badge-cell">
                        {% if request.action %}
                        <div class="badge-container">
                            <div class="badge {% if request.action == 'BUY' %}badge-success{% else %}badge-error{% endif %}">
                                {{ request.action }}
                            </div>
                        </div>
                        {% endif %}
                    </td>
                    <td class="badge-cell">
                        <div class="badge-container">
                            <div class="badge status-badge {% if request.analysis.issues %}badge-warning{% else %}badge-success{% endif %}">
                                {% if request.analysis.issues %}Issues{% else %}Valid{% endif %}
                            </div>
                        </div>
                    </td>
                    <td class="badge-cell">
                        <div class="badge-container">
                            <button class="btn btn-sm btn-primary view-details" 
                                    data-request='{{ request.request_data|tojson }}'
                                    data-response='{{ request.response_data|tojson if request.response_data else request.analysis|tojson }}'>
                                View
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Request Details Modal -->
<dialog id="requestModal" class="modal">
    <div class="modal-box w-11/12 max-w-5xl">
        <h3 class="font-bold text-lg mb-4">Request Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h4 class="font-semibold mb-2">Request Data</h4>
                <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto" id="request-data"></pre>
            </div>
            <div>
                <h4 class="font-semibold mb-2">Response Data</h4>
                <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto" id="response-data"></pre>
            </div>
        </div>
        <div class="modal-action">
            <form method="dialog">
                <button class="btn">Close</button>
            </form>
        </div>
    </div>
</dialog>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to all view buttons
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', function() {
            const requestData = JSON.parse(this.getAttribute('data-request'));
            const responseData = JSON.parse(this.getAttribute('data-response'));
            
            // Remove apikey from request data if present
            if (requestData.apikey) {
                delete requestData.apikey;
            }
            
            document.getElementById('request-data').textContent = 
                JSON.stringify(requestData, null, 2);
            document.getElementById('response-data').textContent = 
                JSON.stringify(responseData, null, 2);
            
            document.getElementById('requestModal').showModal();
        });
    });
});
</script>
{% endblock %}
