{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "Bash(LOG_LEVEL=DEBUG python3 test_colored_logging.py)", "Bash(rm:*)", "Bash(FORCE_COLOR=1 python3 debug_colors.py)", "Bash(rg:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "Bash(ls:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(source:*)", "Bash(.venv/Scripts/python.exe:*)", "WebFetch(domain:docs.openalgo.in)"], "deny": []}}