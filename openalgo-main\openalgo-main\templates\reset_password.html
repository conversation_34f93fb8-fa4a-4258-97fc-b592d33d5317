{% extends "layout.html" %}

{% block title %}Reset Password{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="max-w-lg mx-auto">
            <div class="card shadow-2xl bg-base-100">
                <div class="card-body">
                    <h2 class="card-title text-2xl mb-4">Reset Password</h2>

                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} mb-4">
                                    {% if category == 'success' %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    {% elif category == 'error' %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    {% elif category == 'info' %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    {% else %}
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                        </svg>
                                    {% endif %}
                                    <span>{{ message }}</span>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% if not email_sent %}
                    <!-- Step 1: Email Form -->
                    <form id="emailForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="email">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Email Address</span>
                            </label>
                            <input type="email" 
                                   name="email" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Enter your email">
                            <label class="label">
                                <span class="label-text-alt">Enter the email address associated with your account</span>
                            </label>
                        </div>
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Continue</button>
                        </div>
                    </form>
                    {% elif not method_selected %}
                    <!-- Step 2: Choose Verification Method -->
                    <div class="space-y-4">
                        <div class="text-center mb-6">
                            <h3 class="text-lg font-semibold">Choose Verification Method</h3>
                            <p class="text-base-content/60 text-sm mt-2">How would you like to verify your identity?</p>
                        </div>

                        <!-- TOTP Method -->
                        <form action="{{ url_for('auth.reset_password') }}" method="post" class="mb-4">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="step" value="select_totp">
                            <input type="hidden" name="email" value="{{ email }}">
                            <div class="card bg-base-200 hover:bg-base-300 transition-colors cursor-pointer" onclick="this.closest('form').submit()">
                                <div class="card-body p-4">
                                    <div class="flex items-center gap-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 rounded-full bg-primary flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="font-semibold">Authenticator App (TOTP)</h4>
                                            <p class="text-sm text-base-content/70">Use your authenticator app to generate a verification code</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-base-content/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>

                        <!-- Email Method -->
                        <form action="{{ url_for('auth.reset_password') }}" method="post">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <input type="hidden" name="step" value="select_email">
                            <input type="hidden" name="email" value="{{ email }}">
                            <div class="card bg-base-200 hover:bg-base-300 transition-colors cursor-pointer" onclick="this.closest('form').submit()">
                                <div class="card-body p-4">
                                    <div class="flex items-center gap-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 rounded-full bg-secondary flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary-content" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="font-semibold">Email Reset Link</h4>
                                            <p class="text-sm text-base-content/70">Receive a password reset link via email</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-base-content/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    {% elif method_selected == 'totp' and not totp_verified %}
                    <!-- Step 3a: TOTP Verification -->
                    <form id="totpForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="totp">
                        <input type="hidden" name="email" value="{{ email }}">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">TOTP Code</span>
                            </label>
                            <input type="text" 
                                   name="totp_code" 
                                   required 
                                   pattern="[0-9]{6}"
                                   maxlength="6"
                                   class="input input-bordered" 
                                   placeholder="Enter 6-digit code">
                            <label class="label">
                                <span class="label-text-alt">Enter the 6-digit code from your authenticator app</span>
                            </label>
                        </div>
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Verify Code</button>
                        </div>
                    </form>
                    {% elif method_selected == 'email' and not email_verified %}
                    <!-- Step 3b: Email Sent Confirmation -->
                    <div class="text-center space-y-4">
                        <div class="flex justify-center">
                            <div class="w-16 h-16 rounded-full bg-success flex items-center justify-center relative">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-success-content absolute inset-0 m-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <rect x="2" y="4" width="20" height="16" rx="2" />
                                    <path d="m22 7-10 5L2 7" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold">Check Your Email</h3>
                        <p class="text-base-content/70">
                            We've sent a password reset link to <strong>{{ email }}</strong>. 
                            Click the link in the email to continue resetting your password.
                        </p>
                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <div class="text-sm">
                                    <strong>Didn't receive the email?</strong>
                                    <ul class="list-disc list-inside mt-2 space-y-1">
                                        <li>Check your spam/junk folder</li>
                                        <li>Make sure {{ email }} is correct</li>
                                        <li>The link expires in 1 hour</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 justify-center">
                            <a href="{{ url_for('auth.reset_password') }}" class="btn btn-outline btn-sm">Try Different Method</a>
                            <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-sm">Back to Login</a>
                        </div>
                    </div>
                    {% else %}
                    <!-- Step 4: New Password -->
                    <form id="passwordForm" action="{{ url_for('auth.reset_password') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <input type="hidden" name="step" value="password">
                        <input type="hidden" name="email" value="{{ email }}">
                        <input type="hidden" name="token" value="{{ token }}">
                        
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">New Password</span>
                            </label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Enter new password">
                            <label class="label">
                                <span class="label-text-alt">Password must be at least 8 characters long</span>
                            </label>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Confirm New Password</span>
                            </label>
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Confirm new password">
                        </div>

                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary">Reset Password</button>
                        </div>
                    </form>
                    {% endif %}

                    <div class="divider">OR</div>

                    <div class="text-center">
                        <a href="{{ url_for('auth.login') }}" class="link link-hover">Back to Login</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long!');
                return;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
            }
        });
    }

    // Auto-focus on input fields
    const emailInput = document.querySelector('input[type="email"]');
    const totpInput = document.querySelector('input[name="totp_code"]');
    const passwordInput = document.getElementById('password');

    if (emailInput) emailInput.focus();
    if (totpInput) totpInput.focus();
    if (passwordInput) passwordInput.focus();
});
</script>
{% endblock %}
